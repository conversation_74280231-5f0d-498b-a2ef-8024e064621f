/* src/index.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body,
#root {
  height: 100%; /* Make html, body, and root div take full viewport height */
  margin: 0;     /* Remove default body margin */
  padding: 0;    /* Remove default padding */
  overflow: hidden; /* Prevent scrolling on the body/root */
  background-color: #111827; /* Optional: Set a base background color (Tailwind gray-900) */
  color-scheme: dark; /* Optional: Hint for browser UI like scrollbars */
}

/* Optional: Ensure box-sizing is consistent */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Optional: Style scrollbars for webkit browsers within the app */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #1f2937; /* gray-800 */
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #4b5563; /* gray-600 */
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #6b7280; /* gray-500 */
}