import React, { useEffect, useRef } from 'react';
import SubtitleItem from './SubtitleItem'; // Adjust path if needed

function SubtitleList({ subtitles, currentSubtitleIndex, onSubtitleClick, isFocusModeEnabled }) {
  const listRef = useRef(null); // Ref for the container div

  // Effect to scroll the active item into view when focus mode is enabled
  useEffect(() => {
    if (isFocusModeEnabled && currentSubtitleIndex !== -1 && listRef.current) {
      // Construct the ID of the active item
      const activeItemId = `subtitle-item-${currentSubtitleIndex}`;
      // Find the element within the list container
      const activeElement = listRef.current.querySelector(`#${activeItemId}`);

      if (activeElement) {
        // console.log(`[Focus Mode] Scrolling to item: ${activeItemId}`);
        activeElement.scrollIntoView({
          behavior: 'smooth', // Use 'auto' for instant scroll
          block: 'center',    // Try 'nearest' or 'start'/'end' if 'center' isn't ideal
          inline: 'nearest'
        });
      } else {
        // console.warn(`[Focus Mode] Could not find element with ID: ${activeItemId}`);
      }
    }
    // Dependencies: Run when focus mode changes or the active subtitle index changes
  }, [currentSubtitleIndex, isFocusModeEnabled]);

  return (
    <div ref={listRef} className="space-y-2 px-1">
      {/* Subtitle count indicator */}
      {subtitles.length > 0 && (
        <div className="text-xs text-gray-400 mb-3 px-2">
          {subtitles.length} subtitles • Click to practice
        </div>
      )}

      {/* Subtitle items */}
      <div className="space-y-1">
        {subtitles.map((sub, index) => (
          <SubtitleItem
            key={index}
            id={`subtitle-item-${index}`}
            text={sub.text}
            startTime={sub.startTime}
            isActive={index === currentSubtitleIndex}
            onClick={() => onSubtitleClick(index)}
          />
        ))}
      </div>

      {/* Empty state */}
      {subtitles.length === 0 && (
        <div className="bg-slate-700 rounded-lg p-6 text-center">
          <p className="text-gray-400 mb-2">No subtitles loaded</p>
          <p className="text-xs text-gray-500">Return to the file selection screen to load subtitle files</p>
        </div>
      )}
    </div>
  );
}

export default SubtitleList;