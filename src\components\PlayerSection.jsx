import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  PlayIcon,
  PauseIcon,
  BackwardIcon,
  ForwardIcon,
  SpeakerWaveIcon,
  SpeakerXMarkIcon,
  ArrowUturnLeftIcon,
} from '@heroicons/react/24/solid';
import SubtitleList from './SubtitleList';

function PlayerSection({ videoUrl, subtitles, onGoBack }) {
  const videoRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [currentSubtitleIndex, setCurrentSubtitleIndex] = useState(-1);
  const [progress, setProgress] = useState(0); // Visual progress state
  const [autoPauseEnabled, setAutoPauseEnabled] = useState(false);

  const [isRepeatMode, setIsRepeatMode] = useState(false);
  const [repeatSubtitleIndex, setRepeatSubtitleIndex] = useState(-1);

  const [isFocusModeEnabled, setIsFocusModeEnabled] = useState(true);

  // State to prevent timeupdate interference right after seek OR manual resume
  const justSeekedOrResumedRef = useRef(false); // Renamed for clarity
  const seekOrResumeTimeoutRef = useRef(null); // Renamed for clarity
  // Flag to indicate if the user is currently dragging the slider
  const isDraggingRef = useRef(false);


  // --- useEffect Hooks ---

  // Load video metadata & reset state
  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      const handleLoadedMetadata = () => {
        if (video.duration && isFinite(video.duration) && video.duration > 0) {
            setDuration(video.duration);
            console.log(`Video metadata loaded. Duration set to: ${video.duration}`);
        } else {
            console.warn(`Video metadata loaded, but duration is invalid: ${video.duration}. Resetting to 0.`);
            setDuration(0);
        }
      };
      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      console.log("PlayerSection: Adding loadedmetadata listener.");

      console.log("PlayerSection: New video URL detected, resetting player state.");
      setIsPlaying(false);
      setCurrentTime(0);
      setProgress(0);
      setCurrentSubtitleIndex(-1);
      setIsRepeatMode(false);
      setRepeatSubtitleIndex(-1);
      setDuration(0);
      justSeekedOrResumedRef.current = false; // Reset flag
      isDraggingRef.current = false;
      clearTimeout(seekOrResumeTimeoutRef.current); // Clear timeout

      video.volume = volume;
      video.muted = isMuted;

      return () => {
        console.log("PlayerSection: Cleaning up loadedmetadata listener.");
        if (videoRef.current) {
            videoRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);
        }
        clearTimeout(seekOrResumeTimeoutRef.current); // Clear timeout on cleanup
      };
    } else {
        setDuration(0);
    }
  }, [videoUrl]); // Removed volume/isMuted from deps

  // Apply volume changes
  useEffect(() => { const video = videoRef.current; if (video) { video.volume = volume; } }, [volume]);

  // Apply mute changes
  useEffect(() => { const video = videoRef.current; if (video) { video.muted = isMuted; } }, [isMuted]);


  // Time updates, subtitle highlighting, auto-pause/repeat pause
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleTimeUpdate = () => {
      // Prevent updates immediately after a seek/manual resume OR while dragging
      if (justSeekedOrResumedRef.current || isDraggingRef.current) {
          // console.log(`[TimeUpdate] Ignored due to justSeekedOrResumedRef=${justSeekedOrResumedRef.current} or isDraggingRef=${isDraggingRef.current}`);
          return;
      }

      const currentVidTime = video.currentTime;
      setCurrentTime(currentVidTime);

      if (duration > 0 && isFinite(duration)) {
        const currentProgress = (currentVidTime / duration) * 100;
        setProgress(currentProgress); // Update visual progress
      } else {
        setProgress(0);
      }

      // Handle subtitle index tracking
      // In Repeat Mode, we never change the subtitle index automatically
      if (isRepeatMode) {
          // Keep the repeatSubtitleIndex as is - don't update currentSubtitleIndex
      } else {
          // Normal mode or Auto-Pause mode
          let activeIndex = -1;

          // Find the active subtitle based on current time
          for (let i = 0; i < subtitles.length; i++) {
            if (currentVidTime >= subtitles[i].startTime && currentVidTime < subtitles[i].endTime) {
              activeIndex = i; break;
            }
          }

          // Update index only if it changed AND we are not currently seeking/resuming
          if (activeIndex !== currentSubtitleIndex && !justSeekedOrResumedRef.current) {
              setCurrentSubtitleIndex(activeIndex);
          }
      }

      // Auto-Pause / Repeat Mode Pause Logic
      const activeSubIndex = isRepeatMode ? repeatSubtitleIndex : currentSubtitleIndex;
      const currentSub = subtitles[activeSubIndex];

      if (currentSub && isPlaying) { // Check isPlaying here
          // Increase the buffer time to ensure we pause well before the end
          // Use a larger buffer (100ms) to ensure we don't play any of the next subtitle
          const pauseTime = currentSub.endTime - 0.1; // Increased buffer to 100ms
          const shouldPause = isRepeatMode || (autoPauseEnabled && !isRepeatMode);

          if (shouldPause && currentVidTime >= pauseTime) {
              // Ensure we are pausing for the *correct* subtitle index
              if (activeSubIndex === (isRepeatMode ? repeatSubtitleIndex : currentSubtitleIndex)) {
                  // Make sure we're not at the very beginning of the subtitle
                  const timeIntoPhrase = video.currentTime - currentSub.startTime;
                  if (timeIntoPhrase < 0.15) {
                      // Too close to the start, don't pause yet
                      // console.log(`[TimeUpdate] Pause condition met but currentTime (${video.currentTime}) is too close to startTime (${currentSub.startTime}). Ignoring pause.`);
                  } else {
                      console.log(`[TimeUpdate] Pausing at end of phrase ${activeSubIndex}. Mode: ${isRepeatMode ? 'Repeat' : (autoPauseEnabled ? 'Auto-Pause' : 'Normal')}. Time: ${currentVidTime}, Pause Target: ${pauseTime}`);

                      // Store the current subtitle index before pausing
                      const pausedSubtitleIndex = activeSubIndex;

                      // Check if there's a next subtitle
                      if (pausedSubtitleIndex < subtitles.length - 1) {
                          const nextSub = subtitles[pausedSubtitleIndex + 1];

                          // Calculate the gap between this subtitle and the next one
                          const gap = nextSub.startTime - currentSub.endTime;
                          console.log(`[TimeUpdate] Gap between subtitles: ${gap * 1000}ms`);

                          // If the gap is less than 100ms, we need to be extra careful
                          if (gap < 0.1) {
                              // For small gaps, pause immediately and ensure we're not too close to the next subtitle
                              if (currentVidTime > nextSub.startTime - 0.05) {
                                  // We've already started playing the next subtitle, seek back slightly
                                  console.log(`[TimeUpdate] Already in next subtitle, seeking back to end of current`);
                                  video.currentTime = currentSub.endTime - 0.05;
                              }

                              // Force an immediate pause
                              video.pause();

                              // Force the subtitle index to stay on the current subtitle
                              if (isRepeatMode) {
                                  // In Repeat Mode, ensure we keep the repeat index
                                  if (currentSubtitleIndex !== repeatSubtitleIndex) {
                                      setCurrentSubtitleIndex(repeatSubtitleIndex);
                                  }
                              } else if (autoPauseEnabled) {
                                  // In Auto-Pause Mode, keep the current subtitle highlighted
                                  setCurrentSubtitleIndex(pausedSubtitleIndex);
                              }
                              return; // Exit early to prevent further processing
                          }
                      }

                      // Normal pause case
                      video.pause();

                      // Force the subtitle index to stay on the subtitle that was playing when we paused
                      if (isRepeatMode) {
                          // In Repeat Mode, ensure we keep the repeat index
                          if (currentSubtitleIndex !== repeatSubtitleIndex) {
                              setCurrentSubtitleIndex(repeatSubtitleIndex);
                          }
                      } else if (autoPauseEnabled) {
                          // In Auto-Pause Mode, keep the current subtitle highlighted
                          setCurrentSubtitleIndex(pausedSubtitleIndex);
                      }
                  }
              }
          }
      }
    };

    video.addEventListener('timeupdate', handleTimeUpdate);
    return () => {
      if (videoRef.current) {
        videoRef.current.removeEventListener('timeupdate', handleTimeUpdate);
      }
    };
  }, [subtitles, duration, isPlaying, autoPauseEnabled, currentSubtitleIndex, isRepeatMode, repeatSubtitleIndex]);


  // --- Helper to set the temporary flag ---
  const setTemporarySeekOrResumeFlag = useCallback(() => {
      justSeekedOrResumedRef.current = true;
      clearTimeout(seekOrResumeTimeoutRef.current);
      seekOrResumeTimeoutRef.current = setTimeout(() => {
          justSeekedOrResumedRef.current = false;
          // console.log("Cleared justSeekedOrResumedRef flag");
      }, 150); // Keep timeout at 150ms
  }, []);


  // --- Player Control Functions ---

  const togglePlayPause = useCallback(() => {
    const video = videoRef.current; if (!video) return;
    if (isRepeatMode) {
        console.log('[togglePlayPause] Exiting Repeat Mode.');
        setIsRepeatMode(false);
        setRepeatSubtitleIndex(-1);
    }
    if (video.paused || video.ended) {
        video.play().catch(err => console.error("[togglePlayPause] Play error:", err));
    } else {
        video.pause();
    }
  }, [isRepeatMode]);

  // --- MODIFIED jumpToPrevPhrase ---
  const jumpToPrevPhrase = useCallback(() => {
    const video = videoRef.current;
    if (!video || !subtitles || subtitles.length === 0) return;

    let targetIndex = -1;

    if (isRepeatMode) {
        console.log('[jumpToPrevPhrase] Triggered in Repeat Mode.');
        targetIndex = Math.max(0, repeatSubtitleIndex - 1);
        if (targetIndex === repeatSubtitleIndex) {
            console.log('[jumpToPrevPhrase] Already at the first phrase. Replaying current.');
             const currentSub = subtitles[repeatSubtitleIndex];
             if (currentSub) {
                 setTemporarySeekOrResumeFlag(); // Set flag
                 video.currentTime = currentSub.startTime;
                 video.play().catch(err => { console.error("[jumpToPrevPhrase] Replay error:", err); justSeekedOrResumedRef.current = false; });
             }
            return;
        }
        console.log(`[jumpToPrevPhrase] New repeat target index: ${targetIndex}`);
        setRepeatSubtitleIndex(targetIndex);
        setCurrentSubtitleIndex(targetIndex);

    } else {
        console.log('[jumpToPrevPhrase] Triggered in Normal Mode.');
        const currentVidTime = video.currentTime;
        let currentIndex = -1;
        for (let i = 0; i < subtitles.length; i++) {
            if (currentVidTime >= subtitles[i].startTime && currentVidTime < subtitles[i].endTime) { currentIndex = i; break; }
            if (i + 1 < subtitles.length && currentVidTime >= subtitles[i].endTime && currentVidTime < subtitles[i+1].startTime) { currentIndex = i; break; }
        }
        if (currentIndex === -1 && subtitles.length > 0 && currentVidTime < subtitles[0].startTime) { currentIndex = 0; }
        else if (currentIndex === -1 && subtitles.length > 0 && currentVidTime >= subtitles[subtitles.length - 1].endTime) { currentIndex = subtitles.length - 1; }

        if (currentIndex === 0 && currentVidTime > subtitles[0].startTime + 0.1) {
            targetIndex = 0;
        } else {
            targetIndex = Math.max(0, currentIndex - 1);
        }
        console.log(`[jumpToPrevPhrase] Current time: ${currentVidTime}, Current detected index: ${currentIndex}, Target index: ${targetIndex}`);
        setCurrentSubtitleIndex(targetIndex);
    }

    if (targetIndex >= 0 && targetIndex < subtitles.length) {
      const targetTime = subtitles[targetIndex].startTime;
      console.log(`[jumpToPrevPhrase] Jumping to time: ${targetTime}`);
      setTemporarySeekOrResumeFlag(); // Set flag
      video.currentTime = targetTime;

      if (video.paused || isRepeatMode) {
          video.play().catch(err => { console.error("[jumpToPrevPhrase] Play error:", err); justSeekedOrResumedRef.current = false; });
      }
    }
  }, [subtitles, isRepeatMode, repeatSubtitleIndex, setTemporarySeekOrResumeFlag]); // Added helper dep

  // --- MODIFIED jumpToNextPhrase ---
  const jumpToNextPhrase = useCallback(() => {
    const video = videoRef.current;
    if (!video || !subtitles || subtitles.length === 0) return;

    let targetIndex = -1;
    const lastIndex = subtitles.length - 1;

    if (isRepeatMode) {
        console.log('[jumpToNextPhrase] Triggered in Repeat Mode.');
        targetIndex = Math.min(lastIndex, repeatSubtitleIndex + 1);
         if (targetIndex === repeatSubtitleIndex) {
            console.log('[jumpToNextPhrase] Already at the last phrase. Replaying current.');
             const currentSub = subtitles[repeatSubtitleIndex];
             if (currentSub) {
                 setTemporarySeekOrResumeFlag(); // Set flag
                 video.currentTime = currentSub.startTime;
                 video.play().catch(err => { console.error("[jumpToNextPhrase] Replay error:", err); justSeekedOrResumedRef.current = false; });
             }
            return;
        }
        console.log(`[jumpToNextPhrase] New repeat target index: ${targetIndex}`);
        setRepeatSubtitleIndex(targetIndex);
        setCurrentSubtitleIndex(targetIndex);

    } else {
        console.log('[jumpToNextPhrase] Triggered in Normal Mode.');
        const currentVidTime = video.currentTime;
        for (let i = 0; i < subtitles.length; i++) {
            if (subtitles[i].startTime > currentVidTime + 0.01) {
                targetIndex = i;
                break;
            }
        }
        if (targetIndex === -1) {
            if (lastIndex >= 0 && currentVidTime < subtitles[lastIndex].endTime) {
                targetIndex = lastIndex;
            } else {
                console.log('[jumpToNextPhrase] Already at or past the last subtitle.');
                return;
            }
        }
        console.log(`[jumpToNextPhrase] Current time: ${currentVidTime}, Target index: ${targetIndex}`);
        setCurrentSubtitleIndex(targetIndex);
    }

    if (targetIndex >= 0 && targetIndex < subtitles.length) {
      const targetTime = subtitles[targetIndex].startTime;
      console.log(`[jumpToNextPhrase] Jumping to time: ${targetTime}`);
      setTemporarySeekOrResumeFlag(); // Set flag
      video.currentTime = targetTime;

      if (video.paused || isRepeatMode) {
          video.play().catch(err => { console.error("[jumpToNextPhrase] Play error:", err); justSeekedOrResumedRef.current = false; });
      }
    }
  }, [subtitles, isRepeatMode, repeatSubtitleIndex, setTemporarySeekOrResumeFlag]); // Added helper dep


  // --- REFINED Seek Handlers ---

  const handleMouseDownOnProgress = () => {
    console.log('[handleMouseDownOnProgress] Dragging started.');
    isDraggingRef.current = true;
  };

  const handleProgressInput = (event) => {
    if (isDraggingRef.current) {
        const seekPercentage = parseFloat(event.target.value);
        setProgress(seekPercentage);
    }
  };

  const handleMouseUpOnProgress = (event) => {
    if (!isDraggingRef.current) {
        console.log('[handleMouseUpOnProgress] Mouse up ignored (was not dragging).');
        return;
    }
    console.log('[handleMouseUpOnProgress] Mouse up detected (end drag).');
    event.stopPropagation();
    isDraggingRef.current = false;

    if (isRepeatMode) {
        console.log('[handleMouseUpOnProgress] Exiting Repeat Mode due to seek.');
        setIsRepeatMode(false);
        setRepeatSubtitleIndex(-1);
    }

    const video = videoRef.current;
    const progressBar = event.currentTarget;

    if (video && progressBar && duration > 0 && isFinite(duration)) {
        const finalProgressValue = parseFloat(progressBar.value);
        if (!isNaN(finalProgressValue)) {
            setProgress(finalProgressValue);
            const finalSeekTime = (finalProgressValue / 100) * duration;
            if (isFinite(finalSeekTime)) {
                console.log(`[handleMouseUpOnProgress] Setting video currentTime to: ${finalSeekTime} (from input value ${finalProgressValue}%)`);
                setTemporarySeekOrResumeFlag(); // Set flag
                video.currentTime = finalSeekTime;
            } else {
                console.warn('[handleMouseUpOnProgress] Calculated final seek time is invalid:', finalSeekTime);
                justSeekedOrResumedRef.current = false;
            }
        } else {
            console.warn('[handleMouseUpOnProgress] Could not parse final progress value from input:', progressBar.value);
            justSeekedOrResumedRef.current = false;
        }
    } else {
         console.warn('[handleMouseUpOnProgress] Cannot set final time, video/progressBar/duration invalid.');
         justSeekedOrResumedRef.current = false;
    }
  };

  // --- End REFINED Seek Handlers ---


  const handleVolumeChange = (event) => {
    const newVolume = parseFloat(event.target.value); setVolume(newVolume);
    const newMutedState = newVolume === 0; if (newMutedState !== isMuted) { setIsMuted(newMutedState); }
  };

  const toggleMute = () => {
    const newMutedState = !isMuted; setIsMuted(newMutedState);
    if (!newMutedState && volume === 0) { setVolume(0.5); }
  };

  const handleSubtitleClick = useCallback((index) => {
    console.log(`[handleSubtitleClick] Attempting click for index: ${index}`);
    if (!subtitles || index < 0 || index >= subtitles.length) { console.warn('[handleSubtitleClick] Invalid index or subtitles array.'); return; }
    const video = videoRef.current; if (!video) { console.warn('[handleSubtitleClick] Video ref is not available.'); return; }
    const targetSubtitle = subtitles[index];
    if (!targetSubtitle || typeof targetSubtitle.startTime !== 'number' || !isFinite(targetSubtitle.startTime)) { console.error('[handleSubtitleClick] Invalid target subtitle or startTime:', targetSubtitle); return; }
    console.log(`[handleSubtitleClick] Target Subtitle Time: ${targetSubtitle.startTime}`);

    setIsRepeatMode(true);
    setRepeatSubtitleIndex(index);
    setCurrentSubtitleIndex(index);
    console.log('[handleSubtitleClick] Set repeat mode state.');

    try {
      setTemporarySeekOrResumeFlag(); // Set flag
      video.currentTime = targetSubtitle.startTime;

      console.log(`[handleSubtitleClick] Set currentTime to: ${targetSubtitle.startTime}`);
      console.log('[handleSubtitleClick] Attempting video.play()...');
      const playPromise = video.play();
      if (playPromise !== undefined) {
        playPromise.then(() => { /* Play resolved */ })
        .catch(err => { console.error("[handleSubtitleClick] video.play() Promise rejected:", err); setIsPlaying(false); setIsRepeatMode(false); setRepeatSubtitleIndex(-1); justSeekedOrResumedRef.current = false; });
      } else { console.warn("[handleSubtitleClick] video.play() did not return a promise."); justSeekedOrResumedRef.current = false; }
    } catch (error) { console.error("[handleSubtitleClick] Error during seek or play attempt:", error); setIsPlaying(false); setIsRepeatMode(false); setRepeatSubtitleIndex(-1); justSeekedOrResumedRef.current = false; }
  }, [subtitles, setTemporarySeekOrResumeFlag]); // Added helper dep


  // --- Keyboard Controls ---
  useEffect(() => {
    const handleKeyDown = (event) => {
      const video = videoRef.current;
      const targetIsRange = event.target.tagName === 'INPUT' && event.target.type === 'range';
      const targetIsEditable = event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' || event.target.isContentEditable;

      if (!video || (targetIsEditable && !targetIsRange)) { return; }
      if (targetIsRange && (event.key === 'ArrowLeft' || event.key === 'ArrowRight')) { return; }

      switch (event.key) {
        case ' ':
          event.preventDefault();
          if (isRepeatMode && repeatSubtitleIndex !== -1) {
            // Refined Repeat Mode Spacebar Logic
            const currentSub = subtitles[repeatSubtitleIndex];
            if (!currentSub) {
                console.warn("[KeyDown Space - Repeat] Could not find subtitle for index:", repeatSubtitleIndex);
                return; // Safety check
            }

            if (!video.paused) {
              // If playing (e.g., user paused mid-phrase), just pause
              console.log('[KeyDown Space - Repeat Mode] Pausing video (mid-phrase).');
              video.pause();
            } else {
              // Check if we're paused at or near the end of the subtitle
              const pauseTime = currentSub.endTime - 0.1; // Match the 100ms buffer used in timeupdate
              const isPausedAtOrAfterEnd = video.currentTime >= pauseTime;

              if (isPausedAtOrAfterEnd) {
                // If paused near the end, replay from the start
                console.log(`[KeyDown Space - Repeat Mode] Replaying phrase ${repeatSubtitleIndex} from start (${currentSub.startTime}).`);

                // Set the flag to prevent timeupdate from changing the subtitle index
                setTemporarySeekOrResumeFlag();

                // Seek to the start of the current subtitle
                video.currentTime = currentSub.startTime;

                // Make sure the correct subtitle is highlighted
                if (currentSubtitleIndex !== repeatSubtitleIndex) {
                  setCurrentSubtitleIndex(repeatSubtitleIndex);
                }

                // Play the video
                video.play().catch(err => {
                  console.error("[KeyDown Space] Play error on replay:", err);
                  justSeekedOrResumedRef.current = false;
                });
              } else {
                // If paused in the middle of the subtitle, resume from current position
                console.log(`[KeyDown Space - Repeat Mode] Resuming from current position (${video.currentTime}).`);

                // Set the flag to prevent timeupdate from changing the subtitle index
                setTemporarySeekOrResumeFlag();

                // Make sure the correct subtitle is highlighted
                if (currentSubtitleIndex !== repeatSubtitleIndex) {
                  setCurrentSubtitleIndex(repeatSubtitleIndex);
                }

                // Play the video from current position
                video.play().catch(err => {
                  console.error("[KeyDown Space] Play error on resume:", err);
                  justSeekedOrResumedRef.current = false;
                });
              }
            }

          } else if (autoPauseEnabled && !isPlaying) {
            // Auto-Pause Mode (and paused)
            console.log('[KeyDown Space - Auto-Pause] Checking current position...');

            // First, determine if we're at the end of a subtitle or in the middle of one
            const currentVidTime = video.currentTime;
            let currentIndex = -1;
            let nextSubIndex = -1;
            let manuallyPaused = false;

            // Find the current subtitle (if any)
            for (let i = 0; i < subtitles.length; i++) {
                if (currentVidTime >= subtitles[i].startTime && currentVidTime < subtitles[i].endTime) {
                    currentIndex = i;
                    break;
                }
            }

            // Check if we're at the end of a subtitle (auto-paused) or in the middle (manually paused)
            if (currentIndex !== -1) {
                const currentSub = subtitles[currentIndex];
                const pauseTime = currentSub.endTime - 0.1; // Match the 100ms buffer used in timeupdate
                const isNearEnd = currentVidTime >= pauseTime;

                // If we're not near the end, we were likely manually paused
                manuallyPaused = !isNearEnd;

                if (manuallyPaused) {
                    // If manually paused in the middle of a subtitle, resume from current position
                    console.log(`[KeyDown Space - Auto-Pause] Resuming from current position (${currentVidTime}).`);
                    setTemporarySeekOrResumeFlag(); // Set flag
                    video.play().catch(err => {
                        console.error("[KeyDown Space] Play error on resume:", err);
                        justSeekedOrResumedRef.current = false;
                    });
                    return;
                }

                // If we're near the end (auto-paused), play the next subtitle
                if (isNearEnd) {
                    nextSubIndex = Math.min(subtitles.length - 1, currentIndex + 1);
                } else {
                    // Fallback: replay the current subtitle
                    nextSubIndex = currentIndex;
                }
            } else {
                // We're not in any subtitle, find the next one
                for (let i = 0; i < subtitles.length; i++) {
                    if (subtitles[i].startTime > currentVidTime + 0.01) {
                        nextSubIndex = i;
                        break;
                    }
                }
            }

            if (nextSubIndex !== -1) {
                const nextSub = subtitles[nextSubIndex];
                console.log(`[KeyDown Space - Auto-Pause] Playing next phrase ${nextSubIndex}. Seeking to ${nextSub.startTime} and playing.`);
                setTemporarySeekOrResumeFlag(); // Set flag
                video.currentTime = nextSub.startTime;
                setCurrentSubtitleIndex(nextSubIndex);
                video.play().catch(err => { console.error("[KeyDown Space] Play error on next phrase:", err); justSeekedOrResumedRef.current = false; });
            } else {
                console.log('[KeyDown Space - Auto-Pause] No next phrase found. Doing nothing.');
            }
          } else {
            // Normal Mode: Space toggles play/pause
            togglePlayPause();
          }
          break;
        case 'ArrowLeft': event.preventDefault(); jumpToPrevPhrase(); break;
        case 'ArrowRight': event.preventDefault(); jumpToNextPhrase(); break;
        default: break;
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => { window.removeEventListener('keydown', handleKeyDown); };
  }, [ isPlaying, isRepeatMode, repeatSubtitleIndex, autoPauseEnabled, currentSubtitleIndex, subtitles, togglePlayPause, jumpToPrevPhrase, jumpToNextPhrase, setTemporarySeekOrResumeFlag ]); // Added helper dep


  // --- Video Event Handlers ---
  const handleVideoPlay = useCallback(() => { setIsPlaying(true); }, []);

  // Enhanced pause handler to maintain correct subtitle highlighting
  const handleVideoPause = useCallback(() => {
    setIsPlaying(false);

    const video = videoRef.current;
    if (!video) return;

    const currentVidTime = video.currentTime;

    // Handle Repeat Mode - ensure we keep the correct subtitle highlighted
    if (isRepeatMode && repeatSubtitleIndex !== -1) {
      // In Repeat Mode, we always want to keep the repeatSubtitleIndex highlighted
      // No need to change anything here
      return;
    }

    // Handle Auto-Pause Mode
    if (autoPauseEnabled) {
      // Find which subtitle we're currently in or just finished
      for (let i = 0; i < subtitles.length; i++) {
        // Check if we're within 100ms of the end of a subtitle
        const isNearEnd = currentVidTime >= (subtitles[i].endTime - 0.1) &&
                          currentVidTime <= (subtitles[i].endTime + 0.05);

        // Check if we're inside a subtitle
        const isInside = currentVidTime >= subtitles[i].startTime &&
                         currentVidTime < subtitles[i].endTime;

        if (isInside || isNearEnd) {
          // Force the subtitle index to this subtitle
          setCurrentSubtitleIndex(i);
          break;
        }
      }
    }
  }, [autoPauseEnabled, isRepeatMode, repeatSubtitleIndex, subtitles]);
  const handleVideoEnded = useCallback(() => {
      console.log('[onEnded event] Video ended.');
      setIsPlaying(false);
      if (isRepeatMode) {
          setIsRepeatMode(false);
          setRepeatSubtitleIndex(-1);
      }
      setProgress(100);
      if (duration > 0 && isFinite(duration)) { setCurrentTime(duration); }
      if (subtitles.length > 0) { setCurrentSubtitleIndex(subtitles.length - 1); }
      else { setCurrentSubtitleIndex(-1); }
  }, [subtitles, duration, isRepeatMode]);


  // --- Formatting & Subtitle Text ---
  const formatTime = (timeInSeconds) => { if (isNaN(timeInSeconds) || timeInSeconds < 0 || !isFinite(timeInSeconds)) return '00:00'; const minutes = Math.floor(timeInSeconds / 60); const seconds = Math.floor(timeInSeconds % 60); return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`; };
  const getActiveSubtitleText = () => { const activeIndex = isRepeatMode ? repeatSubtitleIndex : currentSubtitleIndex; if (activeIndex >= 0 && activeIndex < subtitles.length) { return subtitles[activeIndex].text; } return ''; };
  const activeSubtitleText = getActiveSubtitleText();

  // --- RETURN JSX ---
  return (
    <div className="flex flex-col md:flex-row w-full h-full bg-gray-900 overflow-hidden p-4 gap-4">
      {/* Left Section - Video Player */}
      <div className="w-full md:w-[70%] h-1/2 md:h-full flex flex-col bg-slate-900 text-white rounded-lg shadow-lg overflow-hidden">
        {/* Video Section Header */}
        <div className="bg-gradient-to-r from-blue-900 to-slate-800 p-3 flex items-center justify-between flex-shrink-0 border-b border-slate-700 h-14">
          <button onClick={onGoBack} className="flex items-center space-x-2 px-3 py-1.5 bg-slate-700 hover:bg-slate-600 rounded text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800" title="Load Different Files">
            <ArrowUturnLeftIcon className="w-4 h-4" /> <span>Load Different Files</span>
          </button>
          <span className="text-base font-medium text-white mx-auto">Video Player</span>
          <div className="w-[100px]"></div> {/* Empty div to balance the layout */}
        </div>
        {/* Video Element Container */}
        <div className="flex-grow relative flex items-center justify-center bg-black overflow-hidden m-3 rounded-md" style={{ minHeight: "300px" }}>
            <video ref={videoRef} src={videoUrl} className="max-w-full max-h-full object-contain" onClick={togglePlayPause} onPlay={handleVideoPlay} onPause={handleVideoPause} onEnded={handleVideoEnded} muted={isMuted} playsInline />
            {activeSubtitleText && ( <div className="absolute bottom-0 left-0 right-0 mb-4 lg:mb-8 px-4 py-2 bg-black bg-opacity-60 text-white text-center text-lg md:text-xl lg:text-2xl pointer-events-none" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.8)' }}>{activeSubtitleText}</div> )}
            {isRepeatMode && ( <div className="absolute top-2 right-2 z-10 bg-blue-600 text-white text-xs font-semibold px-2 py-1 rounded shadow">Repeat Mode</div> )}
        </div>
        {/* Controls Section */}
        <div className="bg-slate-800 p-4 space-y-3 flex-shrink-0 rounded-b-lg border-t border-slate-700 mx-3 mb-3">
          {/* Progress Bar - REFINED HANDLERS */}
          <input
            type="range"
            min="0" max="100" step="0.1"
            value={progress} // Controlled by visual progress state
            onMouseDown={handleMouseDownOnProgress} // Set dragging flag
            onInput={handleProgressInput} // Update visual state ONLY (using onInput)
            onMouseUp={handleMouseUpOnProgress} // Perform actual seek on release, clear dragging flag
            disabled={!duration || duration <= 0 || !isFinite(duration)}
            className="w-full h-2 bg-slate-600 rounded-lg appearance-none cursor-pointer accent-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Video progress"
          />
          {/* Main Controls Row */}
          <div className="flex items-center justify-between text-white">
            {/* Left Controls */}
            <div className="flex items-center space-x-3">
              <button onClick={jumpToPrevPhrase} title="Previous Phrase (←)" className="p-1 hover:bg-slate-700 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800"> <BackwardIcon className="w-6 h-6" /> </button>
              <button onClick={togglePlayPause} title={isPlaying ? "Pause (Space)" : "Play (Space)"} className="bg-blue-600 hover:bg-blue-700 rounded-full p-1.5 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-slate-800"> {isPlaying ? <PauseIcon className="w-6 h-6" /> : <PlayIcon className="w-6 h-6" />} </button>
              <button onClick={jumpToNextPhrase} title="Next Phrase (→)" className="p-1 hover:bg-slate-700 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800"> <ForwardIcon className="w-6 h-6" /> </button>
            </div>
            {/* Center Time */}
            <div className="text-sm text-slate-300 tabular-nums"> {formatTime(currentTime)} / {formatTime(duration)} </div>
            {/* Right Controls */}
            <div className="flex items-center space-x-3">
                {/* Auto-pause Toggle */}
                <div className="flex items-center" title="Toggle auto-pause after each phrase">
                    <input type="checkbox" id="autoPauseToggle" checked={autoPauseEnabled} onChange={(e) => setAutoPauseEnabled(e.target.checked)} className="form-checkbox h-4 w-4 text-blue-500 bg-slate-600 border-slate-500 rounded focus:ring-blue-600 focus:ring-offset-slate-800 cursor-pointer mr-1" />
                    <label htmlFor="autoPauseToggle" className="text-xs text-slate-400 cursor-pointer select-none">Auto-Pause</label>
                </div>
                {/* Volume Controls */}
                <button onClick={toggleMute} title={isMuted ? "Unmute" : "Mute"} className="p-1 hover:bg-slate-700 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800"> {isMuted || volume === 0 ? <SpeakerXMarkIcon className="w-6 h-6" /> : <SpeakerWaveIcon className="w-6 h-6" />} </button>
                <input type="range" min="0" max="1" step="0.05" value={isMuted ? 0 : volume} onChange={handleVolumeChange} className="w-20 h-1.5 bg-slate-600 rounded-lg appearance-none cursor-pointer accent-blue-500" aria-label="Volume control" />
            </div>
          </div>
        </div>
      </div> {/* End Left Section */}

      {/* Right Section - Subtitles */}
      <div className="w-full md:w-[30%] h-1/2 md:h-full bg-slate-900 flex flex-col rounded-lg shadow-lg overflow-hidden border border-slate-700 text-white">
        {/* Subtitle Section Header */}
        <div className="bg-gradient-to-r from-slate-800 to-blue-900 p-3 flex items-center justify-between flex-shrink-0 border-b border-slate-700 h-14">
          <div className="w-[100px]"></div> {/* Empty div to balance the layout */}
          <span className="text-base font-medium text-white mx-auto">Subtitles</span>
          <label htmlFor="focusModeToggle" className="flex items-center space-x-2 cursor-pointer" title="Toggle Focus Mode (keeps active subtitle visible)">
             <span className="text-sm font-medium text-white">Focus</span>
             <div className="relative inline-flex items-center">
                <input type="checkbox" id="focusModeToggle" className="sr-only peer" checked={isFocusModeEnabled} onChange={(e) => setIsFocusModeEnabled(e.target.checked)} />
                <div className="w-11 h-6 bg-slate-600 rounded-full peer peer-focus:ring-2 peer-focus:ring-blue-500 peer-focus:ring-offset-slate-800 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
             </div>
          </label>
        </div>
        {/* Subtitle List Container */}
        <div className="flex-grow overflow-y-auto p-4 bg-slate-800" style={{ minHeight: "300px" }}>
          <SubtitleList subtitles={subtitles} currentSubtitleIndex={isRepeatMode ? repeatSubtitleIndex : currentSubtitleIndex} onSubtitleClick={handleSubtitleClick} isFocusModeEnabled={isFocusModeEnabled} />
        </div>
      </div> {/* End Right Section */}
    </div> // End Main Container
  );
}

export default PlayerSection;