import React from 'react';

function FileInputSection({ videoFile, subtitleFile, onVideoChange, onSubtitleChange }) {
  return (
    <div className="bg-gray-800 p-8 rounded-lg shadow-lg w-full max-w-md flex flex-col items-center space-y-6">
      {/* Video File Input */}
      <div className="w-full">
        <label
          htmlFor="video-upload"
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg cursor-pointer transition duration-200 ease-in-out block text-center"
        >
          Load Video File (.mp4)
        </label>
        <input
          id="video-upload"
          type="file"
          accept=".mp4"
          onChange={onVideoChange}
          className="hidden" // Hide the default input
        />
        {videoFile && (
          <p className="mt-2 text-sm text-gray-400 text-center">
            Selected: {videoFile.name}
          </p>
        )}
        {!videoFile && (
           <p className="mt-2 text-sm text-gray-500 text-center">
            No video file chosen
          </p>
        )}
      </div>

      {/* Subtitle File Input */}
      <div className="w-full">
        <label
          htmlFor="subtitle-upload"
          className={`font-bold py-3 px-6 rounded-lg cursor-pointer transition duration-200 ease-in-out block text-center ${
            videoFile
              ? 'bg-green-600 hover:bg-green-700 text-white'
              : 'bg-gray-600 text-gray-400 cursor-not-allowed' // Disabled look
          }`}
        >
          Load Subtitle File (.vtt)
        </label>
        <input
          id="subtitle-upload"
          type="file"
          accept=".vtt" // Initially only VTT, can add .srt later
          onChange={onSubtitleChange}
          className="hidden" // Hide the default input
          disabled={!videoFile} // Disable if no video file selected
        />
         {subtitleFile && videoFile && (
          <p className="mt-2 text-sm text-gray-400 text-center">
            Selected: {subtitleFile.name}
          </p>
        )}
        {!subtitleFile && videoFile && (
           <p className="mt-2 text-sm text-gray-500 text-center">
            No subtitle file chosen
          </p>
        )}
         {/* Placeholder text when disabled */}
         {!videoFile && (
           <p className="mt-2 text-sm text-gray-600 text-center">
            Load video file first
          </p>
         )}
      </div>
    </div>
  );
}

export default FileInputSection;