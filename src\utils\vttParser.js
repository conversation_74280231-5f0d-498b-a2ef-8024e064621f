// src/utils/vttParser.js

// Function to convert VTT time format (HH:MM:SS.mmm) to seconds
function timeToSeconds(timeString) {
    if (!timeString) return 0;
    // Handle potential format variations (e.g., missing hours)
    const parts = timeString.split(':');
    let seconds = 0;
    try {
      if (parts.length === 3) { // HH:MM:SS.mmm
        seconds += parseFloat(parts[0]) * 3600;
        seconds += parseFloat(parts[1]) * 60;
        seconds += parseFloat(parts[2].replace(',', '.')); // Handle comma decimal separator
      } else if (parts.length === 2) { // MM:SS.mmm
        seconds += parseFloat(parts[0]) * 60;
        seconds += parseFloat(parts[1].replace(',', '.'));
      } else {
        // Attempt to parse as seconds directly if format is unexpected
        seconds = parseFloat(timeString.replace(',', '.'));
      }
    } catch (e) {
      console.error("Error parsing time:", timeString, e);
      return 0; // Return 0 on error
    }
    return isNaN(seconds) ? 0 : seconds; // Ensure result is not NaN
  }
  
  // Main VTT parsing function
  export function parseVTT(vttContent) {
    if (!vttContent || typeof vttContent !== 'string') {
      console.error("Invalid VTT content provided to parser.");
      return [];
    }
  
    const lines = vttContent.trim().split(/\r?\n/);
    const subtitles = [];
    let currentCue = null;
    let lineIndex = 0;
  
    // Skip initial lines until the first cue identifier or timestamp
    while (lineIndex < lines.length && !lines[lineIndex].includes('-->') && !/^\d+$/.test(lines[lineIndex].trim())) {
        // Skip WEBVTT header, comments, style blocks etc.
        if (lines[lineIndex].trim() === '' || lines[lineIndex].trim().toUpperCase() === 'WEBVTT' || lines[lineIndex].trim().startsWith('NOTE') || lines[lineIndex].trim().startsWith('STYLE')) {
           lineIndex++;
           continue;
        }
        // If we encounter something unexpected before the first cue, break or log
        // console.warn("Skipping unexpected line before first cue:", lines[lineIndex]);
        lineIndex++; // Move past potentially malformed lines
    }
  
  
    for (; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex].trim();
  
      if (line === '') {
        // Blank line signifies the end of a cue's text block
        if (currentCue) {
          // Clean up text before pushing
          currentCue.text = currentCue.text.trim().replace(/<[^>]+>/g, ''); // Remove HTML tags like <i>
          if (currentCue.startTime !== null && currentCue.endTime !== null && currentCue.text) {
              subtitles.push(currentCue);
          } else {
              console.warn("Skipping incomplete cue:", currentCue);
          }
          currentCue = null; // Reset for the next cue
        }
        continue;
      }
  
      // Check for timestamp line (e.g., "00:00:01.000 --> 00:00:05.000")
      if (line.includes('-->')) {
        // If we encounter a timestamp while already processing a cue's text,
        // it likely means the previous cue ended without a blank line.
        if (currentCue && currentCue.text) {
           currentCue.text = currentCue.text.trim().replace(/<[^>]+>/g, '');
           if (currentCue.startTime !== null && currentCue.endTime !== null) {
               subtitles.push(currentCue);
           } else {
               console.warn("Skipping incomplete cue before new timestamp:", currentCue);
           }
        }
  
        const times = line.split('-->');
        if (times.length === 2) {
          const startTime = timeToSeconds(times[0].trim());
          const endTime = timeToSeconds(times[1].trim().split(' ')[0]); // Split off potential style info
  
          if (!isNaN(startTime) && !isNaN(endTime)) {
              currentCue = {
                  id: subtitles.length, // Simple sequential ID
                  startTime: startTime,
                  endTime: endTime,
                  text: ''
              };
          } else {
              console.warn("Failed to parse time string:", line);
              currentCue = null; // Discard if times are invalid
          }
        } else {
            console.warn("Malformed time line:", line);
            currentCue = null; // Discard if format is wrong
        }
        continue; // Move to the next line (should be text)
      }
  
      // If it's not a blank line or a timestamp, assume it's text for the current cue
      if (currentCue) {
        // Append text, adding a space if the text block spans multiple lines
        currentCue.text += (currentCue.text ? ' ' : '') + line;
      } else {
          // This handles cases where text appears before the first valid timestamp
          // or after a malformed cue. We generally skip such lines.
          // console.warn("Skipping text line outside of a valid cue:", line);
      }
    }
  
     // Add the last cue if the file didn't end with a blank line
     if (currentCue && currentCue.text && currentCue.startTime !== null && currentCue.endTime !== null) {
         currentCue.text = currentCue.text.trim().replace(/<[^>]+>/g, '');
         subtitles.push(currentCue);
     } else if (currentCue) {
         console.warn("Skipping incomplete final cue:", currentCue);
     }
  
  
    console.log(`Parsed ${subtitles.length} subtitle cues.`);
    return subtitles;
  }