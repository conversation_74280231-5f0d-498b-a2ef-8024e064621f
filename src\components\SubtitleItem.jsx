import React from 'react';

// Added id prop
function SubtitleItem({ id, text, startTime, isActive, onClick }) {
  // Function to format time (optional, can be removed if not needed here)
  const formatTime = (timeInSeconds) => {
    if (isNaN(timeInSeconds) || timeInSeconds < 0) return '00:00.000';
    const totalMs = Math.floor(timeInSeconds * 1000);
    const ms = String(totalMs % 1000).padStart(3, '0');
    const totalSec = Math.floor(totalMs / 1000);
    const sec = String(totalSec % 60).padStart(2, '0');
    const min = String(Math.floor(totalSec / 60)).padStart(2, '0');
    return `${min}:${sec}.${ms}`; // Example format
  };

  return (
    <div
      id={id}
      onClick={onClick}
      className={`p-3 mb-2 rounded-md cursor-pointer transition-all duration-200 ease-in-out border ${
        isActive
          ? 'bg-blue-600 text-white shadow-md border-blue-500 transform scale-[1.02]' // Enhanced active styles
          : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-gray-100 border-gray-600 hover:border-gray-500' // Enhanced inactive styles
      }`}
      title={`Jump to ${formatTime(startTime)}`}
    >
      <span className="block">{text}</span>
    </div>
  );
}

export default SubtitleItem;