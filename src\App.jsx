import React, { useState, useEffect, useCallback, useRef } from 'react';
import PlayerSection from './components/PlayerSection'; // Adjust path if needed
import { ArrowUpTrayIcon } from '@heroicons/react/24/outline'; // For upload icon

// =============================================
// >>> PARSER FUNCTIONS <<<
// =============================================

// --- VTT Parser ---
function parseVTT(vttContent) {
  if (!vttContent || typeof vttContent !== 'string') {
    console.error("Invalid VTT content provided");
    return [];
  }
  console.log("Attempting to parse VTT content...");
  const lines = vttContent.trim().split(/\r?\n/);
  const cues = [];
  let currentCue = null;
  let cueIdCounter = 0; // Simple counter for unique IDs

  // Check for WEBVTT header
  if (!lines[0].startsWith('WEBVTT')) {
    console.warn("VTT file missing WEBVTT header.");
    // Allow parsing to continue, but log a warning
  }

  function timeStringToSeconds(timeString) {
    if (!timeString || typeof timeString !== 'string') return NaN;
    const parts = timeString.split(':');
    if (parts.length < 2 || parts.length > 3) return NaN; // Basic validation

    let hours = 0, minutes = 0, seconds = 0;

    if (parts.length === 3) { // HH:MM:SS.ms
      hours = parseInt(parts[0], 10);
      minutes = parseInt(parts[1], 10);
      seconds = parseFloat(parts[2].replace(',', '.')); // Allow comma or period for ms
    } else { // MM:SS.ms
      minutes = parseInt(parts[0], 10);
      seconds = parseFloat(parts[1].replace(',', '.')); // Allow comma or period for ms
    }

    if (isNaN(hours) || isNaN(minutes) || isNaN(seconds)) {
      console.warn(`Failed to parse time component: ${timeString}`);
      return NaN;
    }
    return (hours * 3600) + (minutes * 60) + seconds;
  }


  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    if (line.includes('-->')) {
      // Timecode line found
      const timeParts = line.split('-->');
      if (timeParts.length === 2) {
        const startTimeStr = timeParts[0].trim();
        const endTimeStr = timeParts[1].trim().split(' ')[0]; // Remove potential settings after time

        const startTime = timeStringToSeconds(startTimeStr);
        const endTime = timeStringToSeconds(endTimeStr);

        if (!isNaN(startTime) && !isNaN(endTime)) {
          currentCue = {
            id: cueIdCounter++, // Assign unique ID
            startTime: startTime,
            endTime: endTime,
            text: ''
          };
        } else {
          console.warn(`Skipping cue due to invalid time format: ${line}`);
          currentCue = null; // Discard if time is invalid
        }
      }
    } else if (line !== '' && currentCue) {
      // Text line for the current cue
      // Ignore lines starting with NOTE or STYLE, or empty lines after header
      if (!line.startsWith('NOTE') && !line.startsWith('STYLE') && !(i === 1 && line === '')) {
          if (currentCue.text) {
            currentCue.text += '\n' + line; // Append multi-line text
          } else {
            currentCue.text = line;
          }
      }
    } else if (line === '' && currentCue) {
      // Empty line signifies end of the current cue block
      if (currentCue.text) { // Only add cues that have text
         cues.push(currentCue);
      } else {
         console.warn(`Skipping cue with time ${currentCue.startTime}-${currentCue.endTime} because it has no text.`);
      }
      currentCue = null; // Reset for the next cue
    }
    // Ignore WEBVTT header line, NOTE lines, style blocks, and index lines if present
  }

  // Add the last cue if the file doesn't end with a blank line
  if (currentCue && currentCue.text) {
    cues.push(currentCue);
  }

  console.log(`Parsed ${cues.length} VTT cues.`);
  return cues;
}


// --- SRT Parser ---
function parseSRT(srtContent) {
  if (!srtContent || typeof srtContent !== 'string') {
    console.error("Invalid SRT content provided");
    return [];
  }
  console.log("Attempting to parse SRT content...");
  const cues = [];
  // Normalize line endings and split into blocks based on double newlines
  const blocks = srtContent.trim().replace(/\r\n/g, '\n').split('\n\n');
  let cueIdCounter = 0; // Simple counter for unique IDs

  function timeStringToSecondsSRT(timeString) {
    if (!timeString || typeof timeString !== 'string') return NaN;
    // SRT uses comma for milliseconds: HH:MM:SS,ms
    const parts = timeString.split(':');
    if (parts.length !== 3) return NaN; // SRT always has HH:MM:SS

    const secondsParts = parts[2].split(',');
    if (secondsParts.length !== 2) return NaN; // Must have seconds and milliseconds

    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    const seconds = parseInt(secondsParts[0], 10);
    const milliseconds = parseInt(secondsParts[1], 10);

    if (isNaN(hours) || isNaN(minutes) || isNaN(seconds) || isNaN(milliseconds)) {
       console.warn(`Failed to parse SRT time component: ${timeString}`);
       return NaN;
    }

    return (hours * 3600) + (minutes * 60) + seconds + (milliseconds / 1000);
  }

  for (const block of blocks) {
    if (!block.trim()) continue; // Skip empty blocks

    const lines = block.trim().split('\n');
    if (lines.length < 3) {
      console.warn(`Skipping invalid SRT block (not enough lines): ${block}`);
      continue; // Need at least index, time, text
    }

    // Line 0: Index (we can ignore it)
    // const index = parseInt(lines[0], 10);

    // Line 1: Timecode
    const timeLine = lines[1];
    if (!timeLine.includes('-->')) {
        console.warn(`Skipping invalid SRT block (missing '-->'): ${block}`);
        continue;
    }
    const timeParts = timeLine.split('-->');
    if (timeParts.length !== 2) {
        console.warn(`Skipping invalid SRT block (malformed time line): ${block}`);
        continue;
    }

    const startTimeStr = timeParts[0].trim();
    const endTimeStr = timeParts[1].trim();

    const startTime = timeStringToSecondsSRT(startTimeStr);
    const endTime = timeStringToSecondsSRT(endTimeStr);

    if (isNaN(startTime) || isNaN(endTime)) {
      console.warn(`Skipping SRT cue due to invalid time format: ${timeLine}`);
      continue; // Skip block if time is invalid
    }

    // Lines 2+: Subtitle Text
    const text = lines.slice(2).join(' ').trim(); // Join multi-line text with spaces

    if (!text) {
        console.warn(`Skipping SRT cue with time ${startTimeStr}-${endTimeStr} because it has no text.`);
        continue; // Skip cues with no text
    }

    cues.push({
      id: cueIdCounter++, // Assign unique ID
      startTime: startTime,
      endTime: endTime,
      text: text
    });
  }

  console.log(`Parsed ${cues.length} SRT cues.`);
  return cues;
}

// =============================================
// >>> END OF PARSER FUNCTIONS <<<
// =============================================


function App() {
  const [videoFile, setVideoFile] = useState(null);
  const [subtitleFile, setSubtitleFile] = useState(null);
  const [videoUrl, setVideoUrl] = useState(null);
  const [subtitles, setSubtitles] = useState([]);
  const [error, setError] = useState('');
  const [showPlayer, setShowPlayer] = useState(false);
  const [videoFileName, setVideoFileName] = useState('');
  const [subtitleFileName, setSubtitleFileName] = useState('');

  // Refs for file inputs to allow resetting
  const videoInputRef = useRef(null);
  const subtitleInputRef = useRef(null);

  // Clean up object URLs when component unmounts or files change
  useEffect(() => {
    return () => {
      if (videoUrl) {
        console.log("Revoking old video object URL:", videoUrl);
        URL.revokeObjectURL(videoUrl);
      }
    };
  }, [videoUrl]);

  const handleVideoFileChange = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('video/')) {
      console.log("Video file selected:", file.name);
      setVideoFile(file);
      setVideoFileName(file.name);
      setError(''); // Clear previous errors

      // Create URL immediately
      const newVideoUrl = URL.createObjectURL(file);
      console.log("Created new video object URL:", newVideoUrl);

      // Revoke previous URL *before* setting the new one
      if (videoUrl) {
        console.log("Revoking previous video object URL:", videoUrl);
        URL.revokeObjectURL(videoUrl);
      }
      setVideoUrl(newVideoUrl);

      // Reset dependent state immediately
      setShowPlayer(false);
      setSubtitles([]);
      setSubtitleFile(null);
      setSubtitleFileName('');
      if (subtitleInputRef.current) {
        subtitleInputRef.current.value = null; // Reset subtitle input visually
      }

    } else {
      console.log("Invalid video file selected or selection cancelled.");
      setVideoFile(null);
      setVideoFileName('');
      if (videoUrl) {
        URL.revokeObjectURL(videoUrl); // Clean up if selection fails
      }
      setVideoUrl(null);
      setError('Please select a valid video file (MP4, WebM, etc.).');
      setShowPlayer(false); // Ensure player is hidden
    }
  };

  const handleSubtitleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const fileNameLower = file.name.toLowerCase();
      console.log("Subtitle file selected:", file.name);

      if (fileNameLower.endsWith('.vtt') || fileNameLower.endsWith('.srt')) {
        setSubtitleFile(file); // Store the file object
        setSubtitleFileName(file.name);
        setError(''); // Clear previous errors
        setShowPlayer(false); // Hide player until parsing is done
        setSubtitles([]); // Clear previous subtitles

        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target.result;
          let parsedSubtitles = [];
          try {
            console.log(`Attempting to parse as ${fileNameLower.endsWith('.srt') ? 'SRT' : 'VTT'}`);
            // --- Determine format and parse ---
            if (fileNameLower.endsWith('.srt')) {
              parsedSubtitles = parseSRT(content);
            } else { // Assume VTT otherwise
              parsedSubtitles = parseVTT(content);
            }
            // --- End format determination ---

            if (parsedSubtitles && parsedSubtitles.length > 0) {
              console.log("Subtitle parsing successful.");
              setSubtitles(parsedSubtitles);
              setError(''); // Clear error on successful parse
            } else {
              console.warn("Parsing finished, but no subtitles were found or file is empty.");
              setSubtitles([]);
              setError('Could not parse subtitles or the file is empty.');
            }
          } catch (parseError) {
            console.error("Error parsing subtitle file:", parseError);
            setSubtitles([]);
            setError(`Error parsing subtitle file: ${parseError.message}`);
          }
        };
        reader.onerror = (e) => {
          console.error("Error reading subtitle file:", e);
          setError('Failed to read the subtitle file.');
          setSubtitles([]);
        };
        reader.readAsText(file);

      } else {
        console.log("Invalid subtitle file type selected.");
        setSubtitleFile(null);
        setSubtitleFileName('');
        setSubtitles([]);
        setError('Please select a valid subtitle file (.vtt or .srt).');
        setShowPlayer(false);
      }
    } else {
        console.log("Subtitle selection cancelled.");
        setSubtitleFile(null);
        setSubtitleFileName('');
        setSubtitles([]);
        setError(''); // Clear error if no file selected
        setShowPlayer(false);
    }
  };

  // Effect to show player only when both valid files are ready and subtitles parsed
  useEffect(() => {
    if (videoFile && videoUrl && subtitleFile && subtitles.length > 0) {
      console.log("Conditions met: Showing player.");
      setShowPlayer(true);
      setError(''); // Clear any residual errors
    } else {
      // console.log("Conditions not met for showing player:", { videoFile: !!videoFile, videoUrl: !!videoUrl, subtitleFile: !!subtitleFile, subtitlesLength: subtitles.length });
      setShowPlayer(false);
    }
  }, [videoFile, videoUrl, subtitleFile, subtitles]); // Dependencies


  // Handler for the "Go Back" button in PlayerSection
  const handleGoBack = useCallback(() => {
    console.log("Go Back clicked.");
    setShowPlayer(false);
    setVideoFile(null);
    setSubtitleFile(null);
    if (videoUrl) {
      URL.revokeObjectURL(videoUrl); // Clean up URL
    }
    setVideoUrl(null);
    setSubtitles([]);
    setError('');
    setVideoFileName('');
    setSubtitleFileName('');

    // Reset file input values visually
    if (videoInputRef.current) {
      videoInputRef.current.value = null;
    }
    if (subtitleInputRef.current) {
      subtitleInputRef.current.value = null;
    }
  }, [videoUrl]); // Dependency on videoUrl for cleanup


  return (
    // ***** MODIFICATION HERE *****
    // Added h-screen to make this div fill the height set by CSS on #root
    // Added flex flex-col to ensure content inside behaves correctly
    <div className="h-screen bg-gray-900 text-white flex flex-col items-center justify-center p-4 font-sans">
      {!showPlayer ? (
        // --- File Upload Section ---
        // This part remains unchanged from your "Previous revision"
        <div className="w-full max-w-2xl bg-gray-800 p-8 rounded-lg shadow-xl text-center">
          <h1 className="text-3xl font-bold mb-6 text-blue-400">ESL Video Practice</h1>
          <p className="mb-8 text-gray-400">Load your own video and subtitle files to start practicing.</p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Video Upload */}
            <div className="flex flex-col items-center">
              <label
                htmlFor="video-upload"
                className="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-3 px-4 rounded-lg cursor-pointer transition duration-200 flex items-center justify-center space-x-2"
              >
                <ArrowUpTrayIcon className="w-6 h-6" />
                <span>Choose Video File</span>
              </label>
              <input
                ref={videoInputRef} // Assign ref
                id="video-upload"
                type="file"
                accept="video/*" // Accept any video format
                onChange={handleVideoFileChange}
                className="hidden" // Hide the default input
              />
              {videoFileName && (
                <p className="mt-3 text-sm text-gray-400 truncate w-full px-2" title={videoFileName}>
                  Selected: {videoFileName}
                </p>
              )}
            </div>

            {/* Subtitle Upload */}
            <div className="flex flex-col items-center">
              <label
                htmlFor="subtitle-upload"
                className="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-3 px-4 rounded-lg cursor-pointer transition duration-200 flex items-center justify-center space-x-2"
              >
                <ArrowUpTrayIcon className="w-6 h-6" />
                <span>Choose Subtitle File</span>
              </label>
              <input
                ref={subtitleInputRef} // Assign ref
                id="subtitle-upload"
                type="file"
                accept=".vtt,.srt" // Allow both VTT and SRT
                onChange={handleSubtitleFileChange}
                className="hidden" // Hide the default input
              />
              {subtitleFileName && (
                <p className="mt-3 text-sm text-gray-400 truncate w-full px-2" title={subtitleFileName}>
                  Selected: {subtitleFileName}
                </p>
              )}
            </div>
          </div>

          {error && (
            <p className="text-red-500 bg-red-900 bg-opacity-30 border border-red-700 p-3 rounded-md text-sm mt-4">
              {error}
            </p>
          )}

          <p className="text-xs text-gray-500 mt-8">
            Supported subtitle formats: VTT (.vtt), SubRip (.srt)
          </p>

        </div>
      ) : (
        // --- Player Section ---
        // Ensure PlayerSection is also designed to fill available space if needed
        <PlayerSection
          videoUrl={videoUrl}
          subtitles={subtitles}
          onGoBack={handleGoBack} // Pass the handler down
        />
      )}
    </div>
  );
}

export default App;